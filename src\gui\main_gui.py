#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Main GUI Module
主要GUI模組
提供測試機資料檢查工具的圖形化界面
"""

import os
import sys
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from datetime import datetime, timedelta
from pathlib import Path
import tempfile
import shutil

# 添加父目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 導入核心模組
from src.core.test_data_checker import TestDataChecker
from src.core.fcst_processor import FCSTProcessor
from src.utils.file_utils import FileUtils, PathUtils
from config.settings import config, UIConfig


class BaseDialog:
    """基礎對話框類"""
    
    def __init__(self, parent, title, size=(400, 300)):
        self.result = None
        self.parent = parent
        
        # 創建頂層視窗
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry(f"{size[0]}x{size[1]}")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中顯示
        self.center_window()
        
        # 設置關閉事件
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_cancel)
    
    def center_window(self):
        """居中顯示視窗"""
        self.dialog.update_idletasks()
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"+{x}+{y}")
    
    def on_cancel(self):
        """取消按鈕事件"""
        self.result = None
        self.dialog.destroy()
    
    def on_ok(self):
        """確定按鈕事件"""
        self.dialog.destroy()


class TimeTypeDialog(BaseDialog):
    """時間區間和測試類型選擇對話框"""
    
    def __init__(self, parent, title="設定參數"):
        super().__init__(parent, title, (450, 350))
        self.setup_widgets()
    
    def setup_widgets(self):
        """設置界面元件"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 標題
        title_label = ttk.Label(main_frame, text="請設定搜尋參數", 
                               font=UIConfig.FONTS['title'])
        title_label.pack(pady=(0, 20))
        
        # 時間區間設定
        time_frame = ttk.LabelFrame(main_frame, text="時間區間設定", padding="10")
        time_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 開始日期
        ttk.Label(time_frame, text="開始日期:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.start_date_var = tk.StringVar()
        self.start_date_var.set(config.get_default_start_date().strftime("%Y%m%d"))
        self.start_date_entry = ttk.Entry(time_frame, textvariable=self.start_date_var, width=15)
        self.start_date_entry.grid(row=0, column=1, sticky=tk.W)
        ttk.Label(time_frame, text="(格式: YYYYMMDD)", 
                 font=UIConfig.FONTS['small']).grid(row=0, column=2, sticky=tk.W, padx=(5, 0))
        
        # 結束日期
        ttk.Label(time_frame, text="結束日期:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.end_date_var = tk.StringVar()
        self.end_date_var.set(config.get_default_end_date().strftime("%Y%m%d"))
        self.end_date_entry = ttk.Entry(time_frame, textvariable=self.end_date_var, width=15)
        self.end_date_entry.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))
        ttk.Label(time_frame, text="(格式: YYYYMMDD)", 
                 font=UIConfig.FONTS['small']).grid(row=1, column=2, sticky=tk.W, padx=(5, 0), pady=(10, 0))
        
        # 測試類型選擇
        type_frame = ttk.LabelFrame(main_frame, text="測試類型選擇", padding="10")
        type_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.test_type_var = tk.StringVar(value="FT")
        ttk.Radiobutton(type_frame, text="FT測試", variable=self.test_type_var, 
                       value="FT").pack(anchor=tk.W)
        ttk.Radiobutton(type_frame, text="CP測試", variable=self.test_type_var, 
                       value="CP").pack(anchor=tk.W, pady=(5, 0))
        
        # 按鈕
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="取消", command=self.on_cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="確定", command=self.on_ok).pack(side=tk.RIGHT)
    
    def on_ok(self):
        """確定按鈕事件"""
        try:
            # 驗證日期格式
            start_str = self.start_date_var.get().strip()
            end_str = self.end_date_var.get().strip()
            
            if len(start_str) != 8 or len(end_str) != 8:
                raise ValueError("日期格式必須為8位數字")
            
            start_date = datetime.strptime(start_str, "%Y%m%d")
            end_date = datetime.strptime(end_str, "%Y%m%d")
            
            if start_date > end_date:
                raise ValueError("開始日期不能晚於結束日期")
            
            # 設定結束時間為當天23:59:59
            end_date = end_date.replace(hour=23, minute=59, second=59)
            
            self.result = {
                'start_date': start_date,
                'end_date': end_date,
                'test_type': self.test_type_var.get()
            }
            
            super().on_ok()
            
        except ValueError as e:
            messagebox.showerror("輸入錯誤", f"日期格式錯誤: {str(e)}")


class ProgressManager:
    """進度管理器"""
    
    def __init__(self, progress_bar, status_label, result_text):
        self.progress_bar = progress_bar
        self.status_label = status_label
        self.result_text = result_text
        self.is_cancelled = False
    
    def update_progress(self, message, progress=None):
        """更新進度"""
        if self.is_cancelled:
            return
            
        # 更新狀態標籤
        self.status_label.config(text=message)
        
        # 更新進度條
        if progress is not None:
            self.progress_bar['value'] = progress
        
        # 添加到結果文本
        self.result_text.insert(tk.END, f"{message}\n")
        self.result_text.see(tk.END)
        
        # 強制更新GUI
        self.result_text.update_idletasks()
    
    def set_progress(self, value):
        """設置進度值"""
        if not self.is_cancelled:
            self.progress_bar['value'] = value
    
    def reset(self):
        """重置進度"""
        self.is_cancelled = False
        self.progress_bar['value'] = 0
        self.status_label.config(text="就緒")
    
    def cancel(self):
        """取消操作"""
        self.is_cancelled = True
        self.status_label.config(text="操作已取消")


class TestDataCheckerGUI:
    """測試機資料檢查工具GUI主類"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.setup_widgets()
        
        # 初始化核心模組
        self.checker = TestDataChecker()
        self.fcst_processor = FCSTProcessor()
        
        # 工作線程
        self.current_thread = None
    
    def setup_window(self):
        """設置主視窗"""
        self.root.title(config.MAIN_WINDOW_TITLE)
        self.root.geometry(config.MAIN_WINDOW_SIZE)
        self.root.minsize(*config.MAIN_WINDOW_MIN_SIZE)
        
        # 設置關閉事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_variables(self):
        """設置變數"""
        self.selected_files = []
        self.selected_folder = ""
        self.current_results = []
    
    def setup_widgets(self):
        """設置界面元件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置網格權重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 標題
        title_label = ttk.Label(main_frame, text="📊 測試機資料檢查工具", 
                               font=UIConfig.FONTS['title'])
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 功能按鈕區域
        self.setup_function_buttons(main_frame)
        
        # 檔案信息區域
        self.setup_file_info_area(main_frame)
        
        # 結果顯示區域
        self.setup_result_area(main_frame)
        
        # 狀態欄
        self.setup_status_bar(main_frame)
    
    def setup_function_buttons(self, parent):
        """設置功能按鈕"""
        button_frame = ttk.LabelFrame(parent, text="功能選擇", padding="15")
        button_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 所有按鈕橫著放在同一行
        button_container = ttk.Frame(button_frame)
        button_container.pack(fill=tk.X)
        
        # 按鈕配置
        button_config = {'width': 12, 'padding': (5, 3)}
        
        ttk.Button(button_container, text="選擇檔案", 
                  command=self.select_files, **button_config).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(button_container, text="開始檢查", 
                  command=self.start_check, **button_config).pack(side=tk.LEFT, padx=(0, 15))
        
        # 分隔線
        separator = ttk.Separator(button_container, orient='vertical')
        separator.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 15))
        
        ttk.Button(button_container, text="單一外包廠分析", 
                  command=self.single_vendor_analysis, **button_config).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(button_container, text="全產品搜尋", 
                  command=self.full_product_search, **button_config).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(button_container, text="從FCST匯入", 
                  command=self.import_from_fcst, **button_config).pack(side=tk.LEFT, padx=(0, 15))
        
        # 分隔線
        separator2 = ttk.Separator(button_container, orient='vertical')
        separator2.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 15))
        
        ttk.Button(button_container, text="取消操作", 
                  command=self.cancel_operation, **button_config).pack(side=tk.LEFT)
    
    def setup_file_info_area(self, parent):
        """設置檔案信息區域"""
        info_frame = ttk.LabelFrame(parent, text="檔案信息", padding="10")
        info_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        info_frame.columnconfigure(0, weight=1)
        info_frame.rowconfigure(1, weight=1)
        
        # 狀態標籤
        self.file_info_label = ttk.Label(info_frame, text="尚未選擇檔案", 
                                        font=('Microsoft YaHei UI', 10, 'bold'))
        self.file_info_label.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 詳細檔案列表（可選擇）- 放大2.5倍
        listbox_frame = ttk.Frame(info_frame)
        listbox_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        listbox_frame.columnconfigure(0, weight=1)
        listbox_frame.rowconfigure(0, weight=1)
        
        # 使用Listbox代替ScrolledText以支援選擇
        self.file_listbox = tk.Listbox(listbox_frame, height=10, selectmode=tk.EXTENDED)
        self.file_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 添加滾動條
        scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.file_listbox.config(yscrollcommand=scrollbar.set)
        
        # 操作按鈕
        button_frame = ttk.Frame(info_frame)
        button_frame.grid(row=2, column=0, sticky=tk.E, pady=(5, 0))
        
        ttk.Button(button_frame, text="移除選中", 
                  command=self.remove_selected_file, width=10).pack(side=tk.RIGHT, padx=(0, 5))
        ttk.Button(button_frame, text="清除全部", 
                  command=self.clear_selection, width=10).pack(side=tk.RIGHT)
    
    def setup_result_area(self, parent):
        """設置結果顯示區域"""
        result_frame = ttk.LabelFrame(parent, text="檢查結果", padding="10")
        result_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        
        # 結果文本區域
        self.result_text = scrolledtext.ScrolledText(result_frame, height=12, width=80)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def setup_status_bar(self, parent):
        """設置狀態欄"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(1, weight=1)
        
        # 狀態標籤
        self.status_label = ttk.Label(status_frame, text="就緒")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 進度條
        self.progress_bar = ttk.Progressbar(status_frame, mode='determinate')
        self.progress_bar.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        # 初始化進度管理器
        self.progress_manager = ProgressManager(self.progress_bar, self.status_label, self.result_text)
    
    # ===================== 檔案選擇方法 =====================
    
    def select_files(self):
        """選擇檔案（支援跨資料夾累積選擇）"""
        file_types = [
            ('支援的檔案', '*.csv *.zip'),
            ('CSV檔案', '*.csv'),
            ('ZIP檔案', '*.zip'),
            ('所有檔案', '*.*')
        ]
        
        files = filedialog.askopenfilenames(
            title="選擇要檢查的檔案（可跨資料夾累積選擇）",
            filetypes=file_types
        )
        
        if files:
            # 累積選擇：將新選擇的檔案加到現有清單中
            new_files = list(files)
            
            # 避免重複添加相同檔案
            for file_path in new_files:
                if file_path not in self.selected_files:
                    self.selected_files.append(file_path)
            
            self.selected_folder = ""
            self.update_file_info()
    
    
    def update_file_info(self):
        """更新檔案信息顯示"""
        # 清空檔案列表顯示
        self.file_listbox.delete(0, tk.END)
        
        if self.selected_files:
            count = len(self.selected_files)
            info = f"✅ 已選擇檔案: {count} 個（可多選後點擊'移除選中'按鈕刪除）"
            
            # 添加檔案到列表框
            for i, file_path in enumerate(self.selected_files, 1):
                display_text = f"[{i}] {os.path.basename(file_path)} → {file_path}"
                self.file_listbox.insert(tk.END, display_text)
        else:
            info = "🔍 尚未選擇檔案（可跨資料夾重複選擇，檔案會累積保存）"
            self.file_listbox.insert(tk.END, "請點擊上方'選擇檔案'按鈕選擇要檢查的檔案")
        
        # 更新顯示
        self.file_info_label.config(text=info)
    
    def clear_selection(self):
        """清除當前選擇"""
        self.selected_files = []
        self.update_file_info()
    
    def remove_selected_file(self):
        """移除選中的檔案"""
        selected_indices = self.file_listbox.curselection()
        
        if not selected_indices:
            messagebox.showinfo("提示", "請先在檔案列表中選擇要移除的檔案")
            return
        
        if not self.selected_files:
            return
        
        # 確認移除
        count = len(selected_indices)
        if count == 1:
            selected_file = os.path.basename(self.selected_files[selected_indices[0]])
            confirm_msg = f"確定要移除檔案 '{selected_file}' 嗎？"
        else:
            confirm_msg = f"確定要移除選中的 {count} 個檔案嗎？"
        
        if not messagebox.askyesno("確認移除", confirm_msg):
            return
        
        # 從後往前移除，避免索引變化影響
        for index in sorted(selected_indices, reverse=True):
            if 0 <= index < len(self.selected_files):
                self.selected_files.pop(index)
        
        self.update_file_info()
    
    # ===================== 檢查功能方法 =====================
    
    def start_check(self):
        """開始檢查"""
        if not self.selected_files:
            messagebox.showwarning("警告", "請先選擇要檢查的檔案")
            return
        
        # 準備檔案列表
        files_to_check = self.selected_files
        
        if not files_to_check:
            messagebox.showwarning("警告", "沒有找到支援的檔案格式")
            return
        
        # 清空結果區域
        self.result_text.delete(1.0, tk.END)
        self.progress_manager.reset()
        
        # 啟動檢查線程
        self.current_thread = threading.Thread(
            target=self.check_files_thread,
            args=(files_to_check,),
            daemon=True
        )
        self.current_thread.start()
    
    def check_files_thread(self, files_to_check):
        """檢查檔案的線程函數"""
        try:
            self.progress_manager.update_progress(f"開始檢查 {len(files_to_check)} 個檔案...")
            
            results = []
            total_files = len(files_to_check)
            
            for i, file_path in enumerate(files_to_check):
                if self.progress_manager.is_cancelled:
                    break
                
                progress = (i / total_files) * 100
                self.progress_manager.set_progress(progress)
                
                self.progress_manager.update_progress(f"正在檢查: {os.path.basename(file_path)}")
                
                # 檢查檔案
                if file_path.lower().endswith('.zip'):
                    result = self.checker.extract_and_check_zip_file(
                        file_path, 
                        progress_callback=self.progress_manager.update_progress
                    )
                else:
                    result = self.checker.check_file(
                        file_path,
                        progress_callback=self.progress_manager.update_progress
                    )
                
                results.append(result)
                
                # 顯示檢查結果
                if result['status'] == 'success':
                    issues_count = len(result['issues'])
                    self.progress_manager.update_progress(
                        f"✅ {result['file']}: 發現 {issues_count} 個問題"
                    )
                else:
                    self.progress_manager.update_progress(
                        f"❌ {result['file']}: {result['message']}"
                    )
            
            if not self.progress_manager.is_cancelled:
                # 保存結果
                self.current_results = results
                self.save_results(results, files_to_check)
                
                # 完成
                self.progress_manager.set_progress(100)
                total_issues = sum(len(r['issues']) for r in results if r['issues'])
                self.progress_manager.update_progress(
                    f"✅ 檢查完成！共處理 {len(results)} 個檔案，發現 {total_issues} 個問題"
                )
        
        except Exception as e:
            self.progress_manager.update_progress(f"❌ 檢查過程中發生錯誤: {str(e)}")
        
        finally:
            self.current_thread = None
    
    def single_vendor_analysis(self):
        """單一外包廠分析"""
        # 選擇資料夾
        folder = filedialog.askdirectory(title="選擇外包廠資料夾")
        if not folder:
            return
        
        # 獲取時間參數
        dialog = TimeTypeDialog(self.root, "單一外包廠分析設定")
        self.root.wait_window(dialog.dialog)
        
        if not dialog.result:
            return
        
        params = dialog.result
        
        # 清空結果區域
        self.result_text.delete(1.0, tk.END)
        self.progress_manager.reset()
        
        # 啟動分析線程
        self.current_thread = threading.Thread(
            target=self.single_vendor_analysis_thread,
            args=(folder, params),
            daemon=True
        )
        self.current_thread.start()
    
    def single_vendor_analysis_thread(self, folder_path, params):
        """單一外包廠分析線程"""
        try:
            start_date = params['start_date']
            end_date = params['end_date']
            test_type = params['test_type']
            
            self.progress_manager.update_progress(
                f"開始單一外包廠分析: {os.path.basename(folder_path)}"
            )
            self.progress_manager.update_progress(
                f"時間區間: {start_date.strftime('%Y-%m-%d')} ~ {end_date.strftime('%Y-%m-%d')}"
            )
            self.progress_manager.update_progress(f"測試類型: {test_type}")
            
            # 搜尋符合條件的檔案
            found_files = self.checker.search_network_files(
                folder_path, test_type, start_date, end_date,
                progress_callback=self.progress_manager.update_progress
            )
            
            if not found_files:
                self.progress_manager.update_progress("❌ 沒有找到符合條件的檔案")
                return
            
            # 檢查找到的檔案
            results = []
            total_files = len(found_files)
            
            for i, file_path in enumerate(found_files):
                if self.progress_manager.is_cancelled:
                    break
                
                progress = (i / total_files) * 100
                self.progress_manager.set_progress(progress)
                
                result = self.checker.extract_and_check_zip_file(
                    file_path,
                    progress_callback=self.progress_manager.update_progress
                )
                results.append(result)
            
            if not self.progress_manager.is_cancelled:
                # 保存結果
                self.current_results = results
                self.save_results(results, found_files, "單一外包廠分析")
                
                # 完成
                self.progress_manager.set_progress(100)
                total_issues = sum(len(r['issues']) for r in results if r['issues'])
                self.progress_manager.update_progress(
                    f"✅ 單一外包廠分析完成！共處理 {len(results)} 個檔案，發現 {total_issues} 個問題"
                )
        
        except Exception as e:
            self.progress_manager.update_progress(f"❌ 分析過程中發生錯誤: {str(e)}")
        
        finally:
            self.current_thread = None
    
    def full_product_search(self):
        """全產品搜尋"""
        # 確認操作
        if not messagebox.askyesno("確認", "全產品搜尋將搜索網路路徑下的所有產品，可能需要較長時間。是否繼續？"):
            return
        
        # 獲取時間參數
        dialog = TimeTypeDialog(self.root, "全產品搜尋設定")
        self.root.wait_window(dialog.dialog)
        
        if not dialog.result:
            return
        
        params = dialog.result
        
        # 清空結果區域
        self.result_text.delete(1.0, tk.END)
        self.progress_manager.reset()
        
        # 啟動搜尋線程
        self.current_thread = threading.Thread(
            target=self.full_product_search_thread,
            args=(params,),
            daemon=True
        )
        self.current_thread.start()
    
    def full_product_search_thread(self, params):
        """全產品搜尋線程"""
        try:
            start_date = params['start_date']
            end_date = params['end_date']
            test_type = params['test_type']
            
            self.progress_manager.update_progress("開始全產品搜尋...")
            self.progress_manager.update_progress(
                f"搜索 {config.DEFAULT_NETWORK_PATH} 下全部型號"
            )
            self.progress_manager.update_progress(
                f"時間區間: {start_date.strftime('%Y-%m-%d')} ~ {end_date.strftime('%Y-%m-%d')}"
            )
            self.progress_manager.update_progress(f"測試類型: {test_type}")
            
            # 搜尋網路路徑下的所有檔案
            found_files = self.checker.search_network_files(
                config.DEFAULT_NETWORK_PATH, test_type, start_date, end_date,
                progress_callback=self.progress_manager.update_progress
            )
            
            if not found_files:
                self.progress_manager.update_progress("❌ 沒有找到符合條件的檔案")
                return
            
            # 檢查找到的檔案
            results = []
            total_files = len(found_files)
            
            for i, file_path in enumerate(found_files):
                if self.progress_manager.is_cancelled:
                    break
                
                progress = (i / total_files) * 100
                self.progress_manager.set_progress(progress)
                
                result = self.checker.extract_and_check_zip_file(
                    file_path,
                    progress_callback=self.progress_manager.update_progress
                )
                results.append(result)
            
            if not self.progress_manager.is_cancelled:
                # 保存結果
                self.current_results = results
                self.save_results(results, found_files, "全產品搜尋")
                
                # 完成
                self.progress_manager.set_progress(100)
                total_issues = sum(len(r['issues']) for r in results if r['issues'])
                self.progress_manager.update_progress(
                    f"✅ 全產品搜尋完成！共處理 {len(results)} 個檔案，發現 {total_issues} 個問題"
                )
        
        except Exception as e:
            self.progress_manager.update_progress(f"❌ 搜尋過程中發生錯誤: {str(e)}")
        
        finally:
            self.current_thread = None
    
    def import_from_fcst(self):
        """從FCST匯入"""
        # 選擇FCST檔案
        file_types = [
            ('Excel檔案', '*.xlsx;*.xls'),
            ('所有檔案', '*.*')
        ]
        
        fcst_file = filedialog.askopenfilename(
            title="選擇FCST檔案",
            filetypes=file_types
        )
        
        if not fcst_file:
            return
        
        # 獲取時間參數
        dialog = TimeTypeDialog(self.root, "FCST匯入設定")
        self.root.wait_window(dialog.dialog)
        
        if not dialog.result:
            return
        
        params = dialog.result
        
        # 清空結果區域
        self.result_text.delete(1.0, tk.END)
        self.progress_manager.reset()
        
        # 啟動FCST處理線程
        self.current_thread = threading.Thread(
            target=self.import_from_fcst_thread,
            args=(fcst_file, params),
            daemon=True
        )
        self.current_thread.start()
    
    def import_from_fcst_thread(self, fcst_file, params):
        """FCST匯入線程"""
        try:
            start_date = params['start_date']
            end_date = params['end_date']
            test_type = params['test_type']
            
            self.progress_manager.update_progress("開始FCST匯入...")
            self.progress_manager.update_progress(f"FCST檔案: {os.path.basename(fcst_file)}")
            
            # 解析FCST檔案
            fcst_data = self.fcst_processor.parse_fcst_file(
                fcst_file,
                progress_callback=self.progress_manager.update_progress
            )
            
            # 搜尋產品
            search_results = self.fcst_processor.search_products_in_network(
                fcst_data, start_date, end_date, test_type,
                progress_callback=self.progress_manager.update_progress
            )
            
            # 檢查找到的檔案
            results = []
            total_files = sum(len(r['found_files']) for r in search_results)
            processed_files = 0
            
            for product_result in search_results:
                if self.progress_manager.is_cancelled:
                    break
                
                for file_path in product_result['found_files']:
                    if self.progress_manager.is_cancelled:
                        break
                    
                    progress = (processed_files / total_files) * 100 if total_files > 0 else 0
                    self.progress_manager.set_progress(progress)
                    
                    result = self.checker.extract_and_check_zip_file(
                        file_path,
                        progress_callback=self.progress_manager.update_progress
                    )
                    results.append(result)
                    processed_files += 1
            
            if not self.progress_manager.is_cancelled:
                # 生成FCST報告
                txt_path, csv_path = self.fcst_processor.generate_fcst_report(
                    search_results, fcst_data, start_date, end_date, test_type,
                    output_dir=config.get_output_dir()
                )
                
                # 如果有檢查結果，也保存標準報告
                if results:
                    self.current_results = results
                    file_paths = []
                    for product_result in search_results:
                        file_paths.extend(product_result['found_files'])
                    self.save_results(results, file_paths, "FCST")
                
                # 完成
                self.progress_manager.set_progress(100)
                found_products = sum(1 for r in search_results if r['status'] == 'found')
                total_issues = sum(len(r['issues']) for r in results if r['issues'])
                
                self.progress_manager.update_progress(
                    f"✅ FCST匯入完成！"
                )
                self.progress_manager.update_progress(
                    f"找到資料的產品: {found_products}/{len(search_results)}"
                )
                self.progress_manager.update_progress(
                    f"共處理 {len(results)} 個檔案，發現 {total_issues} 個問題"
                )
                self.progress_manager.update_progress(
                    f"FCST報告已保存: {os.path.basename(txt_path)}"
                )
        
        except Exception as e:
            self.progress_manager.update_progress(f"❌ FCST匯入過程中發生錯誤: {str(e)}")
        
        finally:
            self.current_thread = None
    
    def cancel_operation(self):
        """取消當前操作"""
        if self.current_thread and self.current_thread.is_alive():
            self.progress_manager.cancel()
            self.progress_manager.update_progress("正在取消操作...")
        else:
            messagebox.showinfo("信息", "沒有正在進行的操作")
    
    # ===================== 結果保存方法 =====================
    
    def save_results(self, results, file_paths, prefix=""):
        """保存檢查結果"""
        try:
            # 確保輸出目錄存在
            output_dir = config.get_output_dir()
            FileUtils.ensure_directory_exists(output_dir)
            
            # 保存結果 - 如果有prefix，則使用自定義檔案名稱
            if prefix:
                # 使用prefix自定義檔案名稱
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                base_name = f"{prefix}_data_check_issues_{timestamp}"
                txt_path = os.path.join(output_dir, f"{base_name}.txt")
                csv_path = os.path.join(output_dir, f"{base_name}.csv")
                
                # 使用TestDataChecker的報告生成方法
                self.checker.save_txt_report(txt_path, results)
                self.checker.save_csv_report(csv_path, results)
            else:
                # 使用標準方法
                txt_path, csv_path = self.checker.save_results(
                    results, file_paths, output_dir
                )
            
            self.progress_manager.update_progress(
                f"📄 報告已保存:"
            )
            self.progress_manager.update_progress(
                f"  TXT: {os.path.basename(txt_path)}"
            )
            self.progress_manager.update_progress(
                f"  CSV: {os.path.basename(csv_path)}"
            )
            
        except Exception as e:
            self.progress_manager.update_progress(f"❌ 保存報告時發生錯誤: {str(e)}")
    
    # ===================== 視窗管理方法 =====================
    
    def on_closing(self):
        """視窗關閉事件"""
        if self.current_thread and self.current_thread.is_alive():
            if messagebox.askyesno("確認", "有操作正在進行中，確定要關閉程式嗎？"):
                self.progress_manager.cancel()
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """運行GUI"""
        self.root.mainloop()


# ===================== 主程式入口 =====================

def main():
    """主程式"""
    try:
        # 檢查網路路徑
        if not config.validate_network_path(config.DEFAULT_NETWORK_PATH):
            messagebox.showwarning(
                "警告", 
                f"無法存取預設網路路徑: {config.DEFAULT_NETWORK_PATH}\n"
                "部分功能可能無法正常使用。"
            )
        
        # 創建並運行GUI
        app = TestDataCheckerGUI()
        app.run()
        
    except Exception as e:
        messagebox.showerror("錯誤", f"程式啟動失敗: {str(e)}")


if __name__ == "__main__":
    main()